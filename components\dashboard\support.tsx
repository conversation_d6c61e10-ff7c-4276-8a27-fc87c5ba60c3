"use client"

import type React from "react"

import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import {
  MessageCircle,
  Phone,
  Mail,
  Clock,
  Send,
  FileText,
  Video,
  Headphones,
  CheckCircle,
  AlertCircle,
  HelpCircle,
  Zap,
} from "lucide-react"
import { useState } from "react"

export default function Support() {
  const [ticketForm, setTicketForm] = useState({
    subject: "",
    category: "",
    priority: "",
    message: "",
  })

  const supportChannels = [

    {
      title: "Email Support",
      description: "Send detailed questions via email",
      icon: Mail,
      availability: "24/7",
      responseTime: "< 4 hours",
      color: "purple",
    },
    {
      title: "Video Call",
      description: "Schedule a screen-sharing session",
      icon: Video,
      availability: "By appointment",
      responseTime: "Same day",
      color: "orange",
    },
  ]

  const recentTickets = [
    {
      id: "TK-001",
      subject: "Withdrawal processing delay",
      category: "Payments",
      status: "resolved",
      priority: "high",
      created: "2024-01-15",
      updated: "2024-01-15",
    },
    {
      id: "TK-002",
      subject: "MetaTrader login issues",
      category: "Technical",
      status: "in-progress",
      priority: "medium",
      created: "2024-01-14",
      updated: "2024-01-15",
    },
    {
      id: "TK-003",
      subject: "Challenge rules clarification",
      category: "General",
      status: "pending",
      priority: "low",
      created: "2024-01-13",
      updated: "2024-01-13",
    },
  ]

  const handleInputChange = (field: string, value: string) => {
    setTicketForm((prev) => ({ ...prev, [field]: value }))
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    console.log("Ticket submitted:", ticketForm)
    // Reset form
    setTicketForm({
      subject: "",
      category: "",
      priority: "",
      message: "",
    })
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case "resolved":
        return "bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-400"
      case "in-progress":
        return "bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-400"
      case "pending":
        return "bg-yellow-100 dark:bg-yellow-900/30 text-yellow-700 dark:text-yellow-400"
      default:
        return "bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300"
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "resolved":
        return <CheckCircle className="w-4 h-4" />
      case "in-progress":
        return <Clock className="w-4 h-4" />
      case "pending":
        return <AlertCircle className="w-4 h-4" />
      default:
        return <HelpCircle className="w-4 h-4" />
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "high":
        return "bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-400"
      case "medium":
        return "bg-yellow-100 dark:bg-yellow-900/30 text-yellow-700 dark:text-yellow-400"
      case "low":
        return "bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-400"
      default:
        return "bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300"
    }
  }

  const getColorClasses = (color: string) => {
    const colorMap = {
      green: {
        bg: "from-green-500/10 to-emerald-500/10 dark:from-green-500/20 dark:to-emerald-500/20",
        border: "border-green-200 dark:border-green-400/20",
        icon: "text-green-600 dark:text-green-400",
        iconBg: "from-green-500 to-emerald-500",
      },
      blue: {
        bg: "from-blue-500/10 to-cyan-500/10 dark:from-blue-500/20 dark:to-cyan-500/20",
        border: "border-blue-200 dark:border-blue-400/20",
        icon: "text-blue-600 dark:text-blue-400",
        iconBg: "from-blue-500 to-cyan-500",
      },
      purple: {
        bg: "from-purple-500/10 to-pink-500/10 dark:from-purple-500/20 dark:to-pink-500/20",
        border: "border-purple-200 dark:border-purple-400/20",
        icon: "text-purple-600 dark:text-purple-400",
        iconBg: "from-purple-500 to-pink-500",
      },
      orange: {
        bg: "from-orange-500/10 to-amber-500/10 dark:from-orange-500/20 dark:to-amber-500/20",
        border: "border-orange-200 dark:border-orange-400/20",
        icon: "text-orange-600 dark:text-orange-400",
        iconBg: "from-orange-500 to-amber-500",
      },
    }
    return colorMap[color as keyof typeof colorMap] || colorMap.green
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="bg-gradient-to-r from-green-500/10 to-emerald-500/10 dark:from-green-500/20 dark:to-emerald-500/20 backdrop-blur-sm border border-green-200 dark:border-green-400/20 rounded-3xl p-8 shadow-xl relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-green-500/5 to-emerald-500/5 dark:from-green-500/10 dark:to-emerald-500/10" />
        <div className="relative z-10">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">Support Center</h1>
          <p className="text-lg text-gray-700 dark:text-white/70 mb-6">
            Get help from our expert support team available 24/7
          </p>
          <div className="flex items-center gap-6">
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 rounded-full bg-green-500 animate-pulse" />
              <span className="text-sm text-gray-600 dark:text-white/60">Live support online</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 rounded-full bg-blue-500" />
              <span className="text-sm text-gray-600 dark:text-white/60">Average response: 2 minutes</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 rounded-full bg-purple-500" />
              <span className="text-sm text-gray-600 dark:text-white/60">Expert traders available</span>
            </div>
          </div>
        </div>
      </div>

      {/* Support Channels */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {supportChannels.map((channel, index) => {
          const colors = getColorClasses(channel.color)
          const IconComponent = channel.icon

          return (
            <div
              key={index}
              className={`bg-gradient-to-br ${colors.bg} backdrop-blur-sm border ${colors.border} rounded-3xl p-6 shadow-xl hover:shadow-2xl transition-all duration-500 hover:scale-[1.02] group cursor-pointer`}
            >
              <div className="text-center">
                <div
                  className={`w-16 h-16 rounded-2xl bg-gradient-to-r ${colors.iconBg} flex items-center justify-center mx-auto mb-4 shadow-lg group-hover:scale-110 transition-transform duration-300`}
                >
                  <IconComponent className="w-8 h-8 text-white" />
                </div>
                <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-2">{channel.title}</h3>
                <p className="text-sm text-gray-700 dark:text-white/70 mb-4">{channel.description}</p>
                <div className="space-y-2">
                  <div className="flex items-center justify-center gap-2">
                    <Clock className="w-4 h-4 text-gray-500 dark:text-white/50" />
                    <span className="text-xs text-gray-600 dark:text-white/60">{channel.availability}</span>
                  </div>
                  <div className="flex items-center justify-center gap-2">
                    <Zap className="w-4 h-4 text-gray-500 dark:text-white/50" />
                    <span className="text-xs text-gray-600 dark:text-white/60">{channel.responseTime}</span>
                  </div>
                </div>
                <Button
                  className={`w-full mt-4 bg-gradient-to-r ${colors.iconBg} text-white hover:scale-105 transition-all duration-300`}
                >
                  Contact Now
                </Button>
              </div>
            </div>
          )
        })}
      </div>

      {/* Create New Ticket */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <div className="bg-white/80 dark:bg-white/5 backdrop-blur-sm border border-gray-200 dark:border-white/10 rounded-3xl p-8 shadow-xl">
          <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">Create Support Ticket</h3>

          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="space-y-2">
              <Label htmlFor="subject" className="text-sm font-medium text-gray-700 dark:text-white/70">
                Subject *
              </Label>
              <Input
                id="subject"
                type="text"
                value={ticketForm.subject}
                onChange={(e) => handleInputChange("subject", e.target.value)}
                className="rounded-xl border-gray-200 dark:border-white/20 bg-white/50 dark:bg-white/5"
                placeholder="Brief description of your issue"
                required
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="category" className="text-sm font-medium text-gray-700 dark:text-white/70">
                  Category *
                </Label>
                <select
                  id="category"
                  value={ticketForm.category}
                  onChange={(e) => handleInputChange("category", e.target.value)}
                  className="w-full rounded-xl border border-gray-200 dark:border-white/20 bg-white/50 dark:bg-white/5 px-4 py-3 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-green-500"
                  required
                >
                  <option value="">Select Category</option>
                  <option value="technical">Technical Issues</option>
                  <option value="payments">Payments & Withdrawals</option>
                  <option value="trading">Trading Questions</option>
                  <option value="account">Account Management</option>
                  <option value="general">General Inquiry</option>
                </select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="priority" className="text-sm font-medium text-gray-700 dark:text-white/70">
                  Priority *
                </Label>
                <select
                  id="priority"
                  value={ticketForm.priority}
                  onChange={(e) => handleInputChange("priority", e.target.value)}
                  className="w-full rounded-xl border border-gray-200 dark:border-white/20 bg-white/50 dark:bg-white/5 px-4 py-3 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-green-500"
                  required
                >
                  <option value="">Select Priority</option>
                  <option value="low">Low</option>
                  <option value="medium">Medium</option>
                  <option value="high">High</option>
                  <option value="urgent">Urgent</option>
                </select>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="message" className="text-sm font-medium text-gray-700 dark:text-white/70">
                Message *
              </Label>
              <Textarea
                id="message"
                value={ticketForm.message}
                onChange={(e) => handleInputChange("message", e.target.value)}
                className="rounded-xl border-gray-200 dark:border-white/20 bg-white/50 dark:bg-white/5 min-h-[120px]"
                placeholder="Please provide detailed information about your issue..."
                required
              />
            </div>

            <Button
              type="submit"
              className="w-full bg-gradient-to-r from-green-600 to-emerald-600 dark:from-green-500 dark:to-emerald-500 text-white hover:from-green-700 hover:to-emerald-700 dark:hover:from-green-600 dark:hover:to-emerald-600 py-4 text-lg hover:scale-[1.02] transition-all duration-300 hover:shadow-lg"
            >
              <Send className="w-5 h-5 mr-2" />
              Submit Ticket
            </Button>
          </form>
        </div>

        {/* Quick Help */}
        <div className="space-y-6">
          <div className="bg-white/80 dark:bg-white/5 backdrop-blur-sm border border-gray-200 dark:border-white/10 rounded-3xl p-6 shadow-xl">
            <h4 className="text-xl font-bold text-gray-900 dark:text-white mb-4">Quick Help</h4>
            <div className="space-y-4">
              <div className="flex items-center gap-3 p-3 bg-gray-50 dark:bg-white/5 rounded-xl hover:bg-gray-100 dark:hover:bg-white/10 transition-colors duration-200 cursor-pointer">
                <div className="w-8 h-8 rounded-lg bg-gradient-to-r from-blue-500 to-cyan-500 flex items-center justify-center">
                  <FileText className="w-4 h-4 text-white" />
                </div>
                <div>
                  <div className="font-medium text-gray-900 dark:text-white">Trading Rules</div>
                  <div className="text-sm text-gray-600 dark:text-white/60">Complete guide to our rules</div>
                </div>
              </div>

              <div className="flex items-center gap-3 p-3 bg-gray-50 dark:bg-white/5 rounded-xl hover:bg-gray-100 dark:hover:bg-white/10 transition-colors duration-200 cursor-pointer">
                <div className="w-8 h-8 rounded-lg bg-gradient-to-r from-green-500 to-emerald-500 flex items-center justify-center">
                  <Video className="w-4 h-4 text-white" />
                </div>
                <div>
                  <div className="font-medium text-gray-900 dark:text-white">Platform Tutorial</div>
                  <div className="text-sm text-gray-600 dark:text-white/60">Learn to use MetaTrader</div>
                </div>
              </div>

              <div className="flex items-center gap-3 p-3 bg-gray-50 dark:bg-white/5 rounded-xl hover:bg-gray-100 dark:hover:bg-white/10 transition-colors duration-200 cursor-pointer">
                <div className="w-8 h-8 rounded-lg bg-gradient-to-r from-purple-500 to-pink-500 flex items-center justify-center">
                  <Headphones className="w-4 h-4 text-white" />
                </div>
                <div>
                  <div className="font-medium text-gray-900 dark:text-white">Withdrawal Guide</div>
                  <div className="text-sm text-gray-600 dark:text-white/60">How to request payouts</div>
                </div>
              </div>
            </div>
          </div>


        </div>
      </div>

      {/* Recent Tickets */}
      <div className="bg-white/80 dark:bg-white/5 backdrop-blur-sm border border-gray-200 dark:border-white/10 rounded-3xl p-8 shadow-xl">
        <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">Recent Support Tickets</h3>

        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="border-b border-gray-200 dark:border-white/10">
                <th className="text-left py-3 px-4 font-semibold text-gray-700 dark:text-white/70">Ticket ID</th>
                <th className="text-left py-3 px-4 font-semibold text-gray-700 dark:text-white/70">Subject</th>
                <th className="text-left py-3 px-4 font-semibold text-gray-700 dark:text-white/70">Category</th>
                <th className="text-left py-3 px-4 font-semibold text-gray-700 dark:text-white/70">Priority</th>
                <th className="text-left py-3 px-4 font-semibold text-gray-700 dark:text-white/70">Status</th>
                <th className="text-left py-3 px-4 font-semibold text-gray-700 dark:text-white/70">Created</th>
                <th className="text-left py-3 px-4 font-semibold text-gray-700 dark:text-white/70">Updated</th>
              </tr>
            </thead>
            <tbody>
              {recentTickets.map((ticket) => (
                <tr
                  key={ticket.id}
                  className="border-b border-gray-100 dark:border-white/5 hover:bg-gray-50/50 dark:hover:bg-white/5 transition-colors duration-200"
                >
                  <td className="py-4 px-4 font-mono text-sm text-gray-900 dark:text-white">{ticket.id}</td>
                  <td className="py-4 px-4 font-medium text-gray-900 dark:text-white">{ticket.subject}</td>
                  <td className="py-4 px-4 text-gray-700 dark:text-white/70 capitalize">{ticket.category}</td>
                  <td className="py-4 px-4">
                    <Badge className={getPriorityColor(ticket.priority)}>
                      <span className="capitalize">{ticket.priority}</span>
                    </Badge>
                  </td>
                  <td className="py-4 px-4">
                    <Badge className={getStatusColor(ticket.status)}>
                      {getStatusIcon(ticket.status)}
                      <span className="ml-1 capitalize">{ticket.status.replace("-", " ")}</span>
                    </Badge>
                  </td>
                  <td className="py-4 px-4 text-gray-700 dark:text-white/70">{ticket.created}</td>
                  <td className="py-4 px-4 text-gray-700 dark:text-white/70">{ticket.updated}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  )
}
