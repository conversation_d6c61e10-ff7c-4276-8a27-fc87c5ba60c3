"use client"

import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import {
  Facebook,
  Twitter,
  Instagram,
  Linkedin,
  Youtube,
  Mail,

  MapPin,
  ArrowRight,
  Shield,
  Award,
  Globe,
} from "lucide-react"
import Image from "next/image"

export default function Footer() {
  const footerLinks = {
    company: [
      { name: "About Us", href: "/about" },
      { name: "Our Team", href: "/team" },
      { name: "Careers", href: "/careers" },
      { name: "Press", href: "/press" },
    ],
    trading: [
      { name: "Trading Challenges", href: "/challenges" },
      { name: "Trading Rules", href: "/rules" },
      { name: "Platforms", href: "/platforms" },
      { name: "Symbols", href: "/trading-symbols" },
      { name: "Economic Calendar", href: "/economic-calendar" },
    ],
    support: [
      { name: "Contact Us", href: "/contact" },
      { name: "Help Center", href: "/help" },
      { name: "FAQ", href: "/faq" },

      { name: "Video Tutorials", href: "/tutorials" },
      { name: "Webinars", href: "/webinars" },
    ],
    legal: [
      { name: "Terms of Service", href: "/terms" },
      { name: "Privacy Policy", href: "/privacy" },
      { name: "Risk Disclosure", href: "/risk" },
      { name: "Compliance", href: "/compliance" },
      { name: "Cookies", href: "/cookies" },
    ],
  }

  const socialLinks = [
    { name: "Facebook", icon: Facebook, href: "#", color: "hover:text-blue-600" },
    { name: "Twitter", icon: Twitter, href: "#", color: "hover:text-blue-400" },
    { name: "Instagram", icon: Instagram, href: "#", color: "hover:text-pink-600" },
    { name: "LinkedIn", icon: Linkedin, href: "#", color: "hover:text-blue-700" },
    { name: "YouTube", icon: Youtube, href: "#", color: "hover:text-red-600" },
  ]

  const certifications = [
    { name: "FCA Regulated", icon: Shield },
    { name: "Award Winner", icon: Award },
    { name: "Global Reach", icon: Globe },
  ]

  return (
    <footer className="bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm border-t border-gray-200 dark:border-gray-700">
      <div className="max-w-6xl mx-auto px-8 md:px-12 lg:px-16">
        {/* Main Footer Content */}
        <div className="py-16 md:py-20">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-12 mb-16">
            {/* Company Info */}
            <div className="lg:col-span-1">
              <div className="mb-8">
                <div className="flex items-center gap-3 mb-6">
                  <div className="w-10 h-10 flex items-center justify-center shadow-lg">
                    <Image
                      src="https://res.cloudinary.com/dufcjjaav/image/upload/v1754115839/180_3_3_ygapel.png"
                      alt="Forex Throne Logo"
                      width={40}
                      height={40}
                      className="w-full h-full object-contain"
                      priority
                    />
                  </div>
                  <span className="text-2xl font-bold text-gray-900 dark:text-white">FxThrone</span>
                </div>
                <p className="text-gray-700 dark:text-white/70 leading-relaxed mb-6">
                  Empowering traders worldwide with innovative funding solutions, cutting-edge technology, and
                  unparalleled support. Join thousands of successful traders who trust FxThrone.
                </p>
                <div className="flex flex-wrap gap-2 mb-6">
                  {certifications.map((cert, index) => {
                    const IconComponent = cert.icon
                    return (
                      <Badge
                        key={index}
                        className="bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-400 border-blue-200 dark:border-blue-400/20"
                      >
                        <IconComponent className="w-3 h-3 mr-1" />
                        {cert.name}
                      </Badge>
                    )
                  })}
                </div>
              </div>

              {/* Newsletter */}
              <div className="bg-gradient-to-r from-blue-500/10 to-cyan-500/10 dark:from-blue-500/20 dark:to-cyan-500/20 rounded-2xl p-6 border border-blue-200 dark:border-blue-400/20">
                <h4 className="text-lg font-bold text-gray-900 dark:text-white mb-3">Stay Updated</h4>
                <p className="text-sm text-gray-700 dark:text-white/70 mb-4">
                  Get the latest trading insights and market updates delivered to your inbox.
                </p>
                <div className="flex gap-2">
                  <Input
                    type="email"
                    placeholder="Enter your email"
                    className="rounded-xl border-gray-200 dark:border-white/20 bg-white/50 dark:bg-white/5 text-sm"
                  />
                  <Button className="bg-gradient-to-r from-blue-600 to-cyan-600 dark:from-blue-500 dark:to-cyan-500 text-white hover:from-blue-700 hover:to-cyan-700 dark:hover:from-blue-600 dark:hover:to-cyan-600 px-4">
                    <ArrowRight className="w-4 h-4" />
                  </Button>
                </div>
              </div>
            </div>

            {/* Links Grid */}
            <div className="lg:col-span-2">
              <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
                <div>
                  <h4 className="text-lg font-bold text-gray-900 dark:text-white mb-6">Company</h4>
                  <ul className="space-y-4">
                    {footerLinks.company.map((link, index) => (
                      <li key={index}>
                        <a
                          href={link.href}
                          className="text-gray-600 dark:text-white/60 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200"
                        >
                          {link.name}
                        </a>
                      </li>
                    ))}
                  </ul>
                </div>

                <div>
                  <h4 className="text-lg font-bold text-gray-900 dark:text-white mb-6">Trading</h4>
                  <ul className="space-y-4">
                    {footerLinks.trading.map((link, index) => (
                      <li key={index}>
                        <a
                          href={link.href}
                          className="text-gray-600 dark:text-white/60 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200"
                        >
                          {link.name}
                        </a>
                      </li>
                    ))}
                  </ul>
                </div>

                <div>
                  <h4 className="text-lg font-bold text-gray-900 dark:text-white mb-6">Support</h4>
                  <ul className="space-y-4">
                    {footerLinks.support.map((link, index) => (
                      <li key={index}>
                        <a
                          href={link.href}
                          className="text-gray-600 dark:text-white/60 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200"
                        >
                          {link.name}
                        </a>
                      </li>
                    ))}
                  </ul>
                </div>

                <div>
                  <h4 className="text-lg font-bold text-gray-900 dark:text-white mb-6">Legal</h4>
                  <ul className="space-y-4">
                    {footerLinks.legal.map((link, index) => (
                      <li key={index}>
                        <a
                          href={link.href}
                          className="text-gray-600 dark:text-white/60 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200"
                        >
                          {link.name}
                        </a>
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            </div>
          </div>

          {/* Contact Info */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-16">
            <div className="flex items-center gap-4 p-6 bg-gray-50 dark:bg-white/5 rounded-2xl">
              <div className="w-12 h-12 rounded-xl bg-gradient-to-r from-blue-500 to-cyan-500 flex items-center justify-center shadow-lg">
                <Mail className="w-6 h-6 text-white" />
              </div>
              <div>
                <div className="font-semibold text-gray-900 dark:text-white">Email Us</div>
                <div className="text-sm text-gray-600 dark:text-white/60"><EMAIL></div>
              </div>
            </div>

            <div className="flex items-center gap-4 p-6 bg-gray-50 dark:bg-white/5 rounded-2xl">
              <div className="w-12 h-12 rounded-xl bg-gradient-to-r from-blue-500 to-cyan-500 flex items-center justify-center shadow-lg">
                <MapPin className="w-6 h-6 text-white" />
              </div>
              <div>
                <div className="font-semibold text-gray-900 dark:text-white">Visit Us</div>
                <div className="text-sm text-gray-600 dark:text-white/60">London, United Kingdom</div>
              </div>
            </div>
          </div>
        </div>

        {/* Bottom Footer */}
        <div className="py-8 border-t border-gray-200 dark:border-gray-700">
          <div className="flex flex-col md:flex-row items-center justify-between gap-6">
            <div className="text-sm text-gray-600 dark:text-white/60 text-center md:text-left">
              © 2024 FxThrone. All rights reserved. Trading involves substantial risk and may not be suitable for
              all investors.
            </div>

            <div className="flex items-center gap-4">
              {socialLinks.map((social, index) => {
                const IconComponent = social.icon
                return (
                  <a
                    key={index}
                    href={social.href}
                    className={`w-10 h-10 rounded-xl bg-gray-100 dark:bg-gray-800 flex items-center justify-center text-gray-600 dark:text-gray-400 ${social.color} transition-all duration-200 hover:scale-110 hover:shadow-lg`}
                    aria-label={social.name}
                  >
                    <IconComponent className="w-5 h-5" />
                  </a>
                )
              })}
            </div>
          </div>
        </div>
      </div>
    </footer>
  )
}
