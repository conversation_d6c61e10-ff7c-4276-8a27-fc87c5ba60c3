"use client"

import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { Check, Star, TrendingUp, Shield, Clock, DollarSign, Zap, ArrowRight } from "lucide-react"
import { useState } from "react"
import Image from "next/image"

const challengeTypeData = {
  "1-phase": {
    name: "One-Step",
    accountSizes: [
      { size: "$1,000", price: 10, discountedPrice: 8 },
      { size: "$3,000", price: 17, discountedPrice: 13 },
      { size: "$5,000", price: 24, discountedPrice: 19 },
      { size: "$10,000", price: 42, discountedPrice: 33 },
      { size: "$25,000", price: 73, discountedPrice: 58 },
      { size: "$50,000", price: 138, discountedPrice: 55 },
      { size: "$100,000", price: 193, discountedPrice: 77 },
      { size: "$200,000", price: 263, discountedPrice: 105 },
      { size: "$500,000", price: 438, discountedPrice: 175 }
    ]
  },
  "2-phase": {
    name: "Two-Step",
    accountSizes: [
      { size: "$1,000", price: 8, discountedPrice: 6 },
      { size: "$3,000", price: 14, discountedPrice: 11 },
      { size: "$5,000", price: 22, discountedPrice: 17 },
      { size: "$10,000", price: 32, discountedPrice: 25 },
      { size: "$25,000", price: 62, discountedPrice: 49 },
      { size: "$50,000", price: 105, discountedPrice: 42 },
      { size: "$100,000", price: 163, discountedPrice: 65 },
      { size: "$200,000", price: 225, discountedPrice: 90 },
      { size: "$500,000", price: 338, discountedPrice: 135 }
    ]
  },
  "hft": {
    name: "HFT",
    accountSizes: [
      { size: "$1,000", price: 15, discountedPrice: 12 },
      { size: "$3,000", price: 28, discountedPrice: 22 },
      { size: "$5,000", price: 43, discountedPrice: 34 },
      { size: "$10,000", price: 64, discountedPrice: 51 },
      { size: "$25,000", price: 112, discountedPrice: 89 },
      { size: "$50,000", price: 213, discountedPrice: 85 },
      { size: "$100,000", price: 288, discountedPrice: 115 },
      { size: "$200,000", price: 420, discountedPrice: 168 },
      { size: "$500,000", price: 675, discountedPrice: 270 }
    ]
  },
  "instal": {
    name: "Instant",
    accountSizes: [
      { size: "$1,000", price: 56, discountedPrice: 45 },
      { size: "$3,000", price: 106, discountedPrice: 85 },
      { size: "$5,000", price: 169, discountedPrice: 135 },
      { size: "$10,000", price: 244, discountedPrice: 195 },
      { size: "$25,000", price: 482, discountedPrice: 385 },
      { size: "$50,000", price: 938, discountedPrice: 375 },
      { size: "$100,000", price: 1875, discountedPrice: 750 },
      { size: "$200,000", price: 3125, discountedPrice: 1250 },
      { size: "$500,000", price: 4625, discountedPrice: 1850 }
    ]
  }
} as const

export default function PricingSection() {
  const [selectedChallengeType, setSelectedChallengeType] = useState<keyof typeof challengeTypeData>("1-phase")
  const [selectedCurrency, setSelectedCurrency] = useState("usd")

  const challengeTypes = [
    { 
      id: "1-phase", 
      name: "1 Phase", 
      active: selectedChallengeType === "1-phase"
    },
    { 
      id: "2-phase", 
      name: "2 Phase", 
      active: selectedChallengeType === "2-phase"
    },
    { 
      id: "hft", 
      name: "HFT", 
      active: selectedChallengeType === "hft" 
    },
    { 
      id: "instal", 
      name: "Instant", 
      active: selectedChallengeType === "instal" 
    }
  ]

  const currencies = [
    { id: "usd", name: "USD", symbol: "$", flag: "🇺🇸", logo: "/images/flags/us.png", active: true },
    { id: "eur", name: "EUR", symbol: "€", flag: "🇪🇺", logo: "/images/flags/eur.png", active: false },
    { id: "cad", name: "CAD", symbol: "C$", flag: "🇨🇦", logo: "/images/flags/cad.png", active: false },
    { id: "gbp", name: "GBP", symbol: "£", flag: "🇬🇧", logo: "/images/flags/gb.png", active: false },
    { id: "aud", name: "AUD", symbol: "A$", flag: "🇦🇺", logo: "/images/flags/au.png", active: false }
  ]

  const getAccountSizes = (currency: string) => {
    const currencyData = currencies.find(c => c.id === currency)
    const symbol = currencyData?.symbol || "$"
    
    return [
      { size: `${symbol}1,000`, price: 6, discountedPrice: 6 },
      { size: `${symbol}3,000`, price: 11, discountedPrice: 11 },
      { size: `${symbol}5,000`, price: 17, discountedPrice: 17 },
      { size: `${symbol}10,000`, price: 25, discountedPrice: 25 },
      { size: `${symbol}25,000`, price: 49, discountedPrice: 49 },
      { size: `${symbol}50,000`, price: 42, discountedPrice: 42 },
      { size: `${symbol}100,000`, price: 65, discountedPrice: 65 },
      { size: `${symbol}200,000`, price: 90, discountedPrice: 90 },
      { size: `${symbol}500,000`, price: 135, discountedPrice: 135 }
    ]
  }



  const getTradingParameters = (challengeType: string) => {
    switch (challengeType) {
      case "1-phase":
        return [
          { parameter: "Profit Target", value: "10%" },
          { parameter: "Daily Drawdown Limit", value: "4%" },
          { parameter: "Overall Drawdown Limit", value: "8%" },
          { parameter: "Minimum Trading Days", value: "5" },
          { parameter: "Leverage", value: "1:100" },
          { parameter: "Profit Split", value: "80%" },
          { parameter: "News Trading", value: "Allowed" },
          { parameter: "Overnight Trading", value: "Allowed" },
          { parameter: "Weekend Holding", value: "Allowed" },
          { parameter: "Consistency Rule", value: "None" },
          { parameter: "Withdrawals", value: "Weekly" },
          { parameter: "EA (Expert Advisor)", value: "Allowed" },
          { parameter: "HFT", value: "Not Allowed" }
        ]
      case "2-phase":
        return [
          { parameter: "Profit Target (Phase 1)", value: "10%" },
          { parameter: "Profit Target (Phase 2)", value: "5%" },
          { parameter: "Daily Drawdown Limit", value: "5%" },
          { parameter: "Overall Drawdown Limit", value: "10%" },
          { parameter: "Minimum Trading Days", value: "5" },
          { parameter: "Leverage", value: "1:100" },
          { parameter: "Profit Split", value: "80%" },
          { parameter: "News Trading", value: "Allowed" },
          { parameter: "Overnight Trading", value: "Allowed" },
          { parameter: "Weekend Holding", value: "Allowed" },
          { parameter: "Consistency Rule", value: "None" },
          { parameter: "Withdrawals", value: "Weekly" },
          { parameter: "EA (Expert Advisor)", value: "Allowed" },
          { parameter: "HFT", value: "Not Allowed" }
        ]
      case "hft":
        return [
          { parameter: "Profit Target", value: "8%" },
          { parameter: "Daily Drawdown Limit", value: "3%" },
          { parameter: "Overall Drawdown Limit", value: "6%" },
          { parameter: "Minimum Trading Days", value: "1" },
          { parameter: "Leverage", value: "1:100" },
          { parameter: "Profit Split", value: "90%" },
          { parameter: "News Trading", value: "Allowed" },
          { parameter: "Overnight Trading", value: "Allowed" },
          { parameter: "Weekend Holding", value: "Not Allowed" },
          { parameter: "Consistency Rule", value: "35% Profit Consistency Rule" },
          { parameter: "Withdrawals", value: "Bi-Weekly" },
          { parameter: "HFT Bot", value: "Allowed" }
        ]
      case "instal":
        return [
          { parameter: "Daily Drawdown Limit", value: "2%" },
          { parameter: "Overall Drawdown Limit", value: "4%" },
          { parameter: "Leverage", value: "1:100" },
          { parameter: "Profit Split", value: "90%" },
          { parameter: "News Trading", value: "Not Allowed" },
          { parameter: "Overnight Trading", value: "Allowed" },
          { parameter: "Weekend Holding", value: "Not Allowed" },
          { parameter: "Consistency Rule", value: "25% Profit Consistency Rule" },
          { parameter: "Withdrawals", value: "Bi-Weekly" },
          { parameter: "EA/HFT", value: "Not Allowed" }
        ]
      default:
        return [
          { parameter: "Profit Target", value: "10%" },
          { parameter: "Daily Drawdown Limit", value: "4%" },
          { parameter: "Overall Drawdown Limit", value: "8%" },
          { parameter: "Minimum Trading Days", value: "5" },
          { parameter: "Leverage", value: "1:100" },
          { parameter: "Profit Split", value: "80%" },
          { parameter: "News Trading", value: "Allowed" },
          { parameter: "Overnight Trading", value: "Allowed" },
          { parameter: "Weekend Holding", value: "Allowed" },
          { parameter: "Consistency Rule", value: "None" },
          { parameter: "Withdrawals", value: "Weekly" },
          { parameter: "EA (Expert Advisor)", value: "Allowed" },
          { parameter: "HFT", value: "Not Allowed" }
        ]
    }
  }

  return (
    <section className="py-24 md:py-32 relative overflow-hidden">
      {/* Background */}
      <div className="absolute inset-0 bg-gradient-to-br from-gray-50 via-white to-gray-100 dark:from-slate-950 dark:via-gray-900 dark:to-slate-950" />
      
      {/* Background Elements */}
      <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-gradient-to-r from-blue-500/5 to-cyan-500/5 dark:from-blue-500/10 dark:to-cyan-500/10 rounded-full blur-3xl" />
      <div className="absolute bottom-1/4 right-1/4 w-80 h-80 bg-gradient-to-r from-indigo-500/5 to-blue-500/5 dark:from-indigo-500/10 dark:to-blue-500/10 rounded-full blur-3xl" />

      <div className="max-w-7xl mx-auto px-8 md:px-12 lg:px-16 relative z-10">
        {/* Header */}
        <div className="text-center mb-20">
          <Badge className="mb-8 bg-gradient-to-r from-blue-600 to-cyan-600 text-white border-0 px-6 py-3 text-sm font-semibold shadow-lg">
            <TrendingUp className="w-4 h-4 mr-2" />
            TRADING CHALLENGES
          </Badge>
          
          <h2 className="text-5xl md:text-7xl lg:text-8xl font-bold mb-8 leading-tight tracking-tight">
            Choose Your
            <span className="block bg-gradient-to-r from-blue-600 via-cyan-600 to-indigo-600 dark:from-blue-400 dark:via-cyan-400 dark:to-indigo-400 bg-clip-text text-transparent">
              Challenge
            </span>
          </h2>
          
          <p className="text-xl md:text-2xl lg:text-3xl text-gray-600 dark:text-white/80 max-w-4xl mx-auto leading-relaxed font-light">
            Start with a small account and scale up to $200K. No time limits, 90% profit split, and free retries.
          </p>
        </div>

        {/* Challenge Type Selectors */}
        <div className="flex flex-col items-center mb-16">
          <div className="bg-white dark:bg-gray-900 rounded-2xl p-4 md:p-6 shadow-lg border border-gray-200 dark:border-gray-700 mb-8 w-full max-w-4xl">
            {/* All Challenge Options Row */}
            <div className="flex items-center justify-center gap-2 md:gap-4 mb-6 flex-wrap">
              {challengeTypes.map((type) => (
                <div key={type.id} className="flex flex-col items-center">
                  <Button
                    variant={type.active ? "default" : "ghost"}
                    className={`rounded-xl px-3 md:px-6 py-2 md:py-3 text-xs md:text-sm font-semibold transition-all duration-300 ${
                      type.active
                        ? "bg-gradient-to-r from-blue-600 to-cyan-600 text-white shadow-lg hover:from-blue-700 hover:to-cyan-700"
                        : "text-gray-600 dark:text-white/70 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100 dark:hover:bg-gray-800"
                    }`}
                    onClick={() => {
                      setSelectedChallengeType(type.id as keyof typeof challengeTypeData)
                      setSelectedCurrency("usd")
                    }}
                  >
                    {type.name}
                  </Button>
                </div>
              ))}
            </div>


          </div>
          
          <Button className="bg-gradient-to-r from-blue-600 to-cyan-600 hover:from-blue-700 hover:to-cyan-700 text-white px-6 md:px-8 py-3 md:py-4 rounded-xl text-sm md:text-base font-semibold hover:scale-105 transition-all duration-300 shadow-lg">
            Get Funded
          </Button>
        </div>

        {/* Pricing Table */}
        <div className="bg-white dark:bg-gray-900 rounded-3xl shadow-2xl overflow-hidden border border-gray-200 dark:border-gray-700">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="bg-gradient-to-r from-blue-100 to-cyan-100 dark:from-blue-900/20 dark:to-cyan-900/20">
                  <th className="text-left p-3 md:p-4 font-bold text-gray-900 dark:text-white text-xs md:text-sm">
                    Trading Parameters
                  </th>
                  {challengeTypeData[selectedChallengeType]?.accountSizes.map((account, index) => (
                    <th key={index} className="text-center p-3 md:p-4 font-bold text-gray-900 dark:text-white text-xs md:text-sm">
                      {account.size}
                    </th>
                  ))}
                </tr>
              </thead>
              <tbody>
                {getTradingParameters(selectedChallengeType).map((param, index) => (
                  <tr 
                    key={index} 
                    className={`border-b border-gray-100 dark:border-gray-800 ${
                      index % 2 === 0 ? "bg-gray-50 dark:bg-gray-800/50" : ""
                    }`}
                  >
                    <td className="p-3 md:p-4 font-semibold text-gray-900 dark:text-white text-xs md:text-sm">
                      {param.parameter}
                    </td>
                    {challengeTypeData[selectedChallengeType]?.accountSizes.map((account, accountIndex) => (
                      <td key={accountIndex} className="text-center p-3 md:p-4 text-gray-700 dark:text-white/70 text-xs md:text-sm font-medium">
                        {param.value}
                      </td>
                    ))}
                  </tr>
                ))}
                <tr className="border-b border-gray-100 dark:border-gray-800 bg-gray-50 dark:bg-gray-800/30">
                  <td className="p-3 md:p-4 font-bold text-gray-900 dark:text-white text-xs md:text-sm">
                    Buy Now
                  </td>
                  {challengeTypeData[selectedChallengeType]?.accountSizes.map((account, index) => (
                    <td key={index} className="text-center p-3 md:p-4">
                      <Button className="bg-white border-2 border-blue-500 text-blue-600 hover:bg-blue-50 dark:bg-gray-800 dark:border-blue-400 dark:text-blue-400 dark:hover:bg-gray-700 px-3 md:px-4 py-1.5 md:py-2 rounded-lg text-xs md:text-sm font-semibold transition-all duration-300 hover:scale-105 shadow-md">
                        ${account.discountedPrice}
            </Button>
                    </td>
                  ))}
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        {/* Additional Features */}
        <div className="mt-20 grid grid-cols-1 md:grid-cols-3 gap-12">
          <div className="text-center group">
            <div className="w-20 h-20 rounded-2xl bg-gradient-to-r from-blue-600 to-cyan-600 flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300 shadow-lg">
              <Shield className="w-10 h-10 text-white" />
            </div>
            <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">Secure Trading</h3>
            <p className="text-gray-600 dark:text-white/70 text-base leading-relaxed">
              Your funds are protected with industry-leading security measures and regulatory compliance
            </p>
          </div>
          
          <div className="text-center group">
            <div className="w-20 h-20 rounded-2xl bg-gradient-to-r from-blue-600 to-cyan-600 flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300 shadow-lg">
              <Clock className="w-10 h-10 text-white" />
            </div>
            <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">No Time Limits</h3>
            <p className="text-gray-600 dark:text-white/70 text-base leading-relaxed">
              Take as long as you need to complete your challenge with flexible trading schedules
            </p>
                </div>

          <div className="text-center group">
            <div className="w-20 h-20 rounded-2xl bg-gradient-to-r from-blue-600 to-cyan-600 flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300 shadow-lg">
              <DollarSign className="w-10 h-10 text-white" />
            </div>
            <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">90% Profit Split</h3>
            <p className="text-gray-600 dark:text-white/70 text-base leading-relaxed">
              Keep 90% of your trading profits once funded with transparent profit sharing
            </p>
          </div>
        </div>

        {/* CTA Section */}
        <div className="text-center mt-24">
          <div className="bg-gradient-to-r from-blue-500/10 to-cyan-500/10 dark:from-blue-500/20 dark:to-cyan-500/20 backdrop-blur-sm border border-blue-200 dark:border-blue-400/20 rounded-3xl p-16 shadow-2xl">
            <h3 className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-6">
              Ready to Start Your Trading Journey?
            </h3>
            <p className="text-xl md:text-2xl text-gray-700 dark:text-white/70 mb-12 max-w-3xl mx-auto leading-relaxed font-light">
              Start with any challenge and scale up to $500K — no time limits and up to 90% profit split.
            </p>
            <div className="flex flex-col sm:flex-row gap-6 justify-center">
              <Button className="bg-gradient-to-r from-blue-600 to-cyan-600 dark:from-blue-500 dark:to-cyan-500 text-white hover:from-blue-700 hover:to-cyan-700 dark:hover:from-blue-600 dark:hover:to-cyan-600 px-10 py-6 text-xl font-semibold hover:scale-105 transition-all duration-300 hover:shadow-xl rounded-2xl">
                <TrendingUp className="w-6 h-6 mr-3" />
                Start Your Challenge
                <ArrowRight className="w-6 h-6 ml-3" />
            </Button>
              <Button variant="outline" className="border-2 border-gray-300 dark:border-white/20 text-gray-700 dark:text-white/70 hover:bg-gray-50 dark:hover:bg-white/5 px-10 py-6 text-xl font-semibold rounded-2xl">
                <Zap className="w-6 h-6 mr-3" />
                Learn More
            </Button>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
